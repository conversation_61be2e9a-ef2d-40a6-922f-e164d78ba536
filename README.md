#简介
node 版本 18.20.5
pnpm 版本 8.15.9
使用 pnpm 工作区

#运行项目
1、本地执行 submodule 的初始化
git submodule init
git submodule update --remote

### 📦 环境要求

- Node.js: 18.20.4
- Git: 支持 submodule

# 🚀 快速开始

## 安装所有工作区依赖

```bash
pnpm install
```

## 启动主应用

```bash
pnpm dev:main
```

## 启动子应用插件

```bash
pnpm dev:vpp
```

## 🔧 开发指南

#### 在主应用中查看独立运行的项目

1. 运行 `pnpm dev:sub` 启动子应用
2. 修改 `Fusion-template\main\src\omega\fusion.js` 文件

```js
//开发环境的配置
const develop = {
  appList: [
    {
      name: "subname", // //想要调试的子应用名称
      url: "//localhost:9529" // 该子应用在本地的运行地址，如果主应用和子应用不在一个ip上需要正确填写内网ip
    }
  ],
  options: {
    systemConfig: "local", // 本地配置，不走权限中心
    isMultiProject: true
  }
};
```

3. 运行 `pnpm dev:main` 启动主应用
4. 在浏览器中访问

### 配置菜单

修改 `plugins\subname\public\config.js` 文件，配置菜单

### 跳过登录

修改 `Fusion-template\main\.env` 文件

```sh
omegaCliDevserver.mockAuth=true
```

## 📦 构建与部署

### 构建命令

```bash
# 构建主应用
pnpm --filter main build

# 构建子应用
pnpm --filter sub build

# 构建并生成分析报告
pnpm --filter main build:report
pnpm --filter sub build:report

# DevOps 构建（主应用）
pnpm --filter main build:devops
```
