<template>
  <div class="test-page">
    <h2>资源聚合-机组列表测试页面</h2>
    <p>页面已成功创建并可以访问</p>

    <!-- 基本表格测试 -->
    <div class="table-test">
      <h3>表格测试</h3>
      <el-table :data="tableData" style="width: 100%" :border="false">
        <el-table-column type="index" label="序号" width="72"></el-table-column>
        <el-table-column prop="unitName" label="机组名称"></el-table-column>
        <el-table-column prop="unitType" label="机组类型"></el-table-column>
        <el-table-column prop="resourceCount" label="聚合资源数量">
          <template slot-scope="scope">
            {{ scope.row.resourceCount }}个
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button type="text" size="small" class="danger-text">
              删除
            </el-button>
            <el-button type="text" size="small">详情</el-button>
            <el-button type="text" size="small">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 按钮测试 -->
    <div class="button-test" style="margin-top: 20px">
      <el-button type="primary" @click="testClick">测试按钮</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "AggregationTest",
  data() {
    return {
      tableData: [
        {
          unitName: "1#调峰机组",
          unitType: "火电机组",
          resourceCount: 25
        },
        {
          unitName: "2#调峰机组",
          unitType: "水电机组",
          resourceCount: 18
        },
        {
          unitName: "3#调峰机组",
          unitType: "风电机组",
          resourceCount: 32
        },
        {
          unitName: "4#调峰机组",
          unitType: "光伏机组",
          resourceCount: 15
        },
        {
          unitName: "5#调峰机组",
          unitType: "燃气机组",
          resourceCount: 42
        }
      ]
    };
  },
  methods: {
    testClick() {
      this.$message.success("测试按钮点击成功！");
    }
  }
};
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.table-test {
  margin-top: 20px;
}

/* 移除表格列分割线 */
.table-test :deep(.el-table) {
  border: none;
}

.table-test :deep(.el-table td),
.table-test :deep(.el-table th) {
  border-right: none;
}

.table-test :deep(.el-table--border) {
  border: none;
}

.table-test :deep(.el-table--border::after) {
  display: none;
}

.table-test :deep(.el-table--border .el-table__cell) {
  border-right: none;
}

/* 统一白色背景 */
.table-test :deep(.el-table th.el-table__cell) {
  background-color: var(--BG1);
  font-weight: 600;
  color: var(--T1);
}

.table-test :deep(.el-table tr) {
  background-color: var(--BG1);
}

.table-test
  :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: var(--BG1);
}

.danger-text {
  color: var(--Sta3);
}

.danger-text:hover {
  color: var(--Sta3);
}
</style>
